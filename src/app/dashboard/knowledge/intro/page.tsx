'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { createClientComponentClient } from '@/utils/supabase/client'
import { useDashboardData } from '@/hooks/useOptimizedData'
import { FaBrain, FaImage, FaComments } from 'react-icons/fa'
import { v4 as uuidv4 } from 'uuid'
import { useLanguage } from '@/context/LanguageContext'

// Optimized PhotoThumbnail component for fast loading without Vercel Image
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  return (
    <div
      className={`${className} bg-zinc-700 rounded overflow-hidden flex-shrink-0 relative ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 bg-zinc-600 transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className="w-full h-full flex items-center justify-center bg-white/5 text-zinc-400 text-xs">
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

export default function IntroPage() {
  // Use dashboard cache for knowledge stats and client info
  const { data: dashboardData, loading: isDashboardLoading } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo
  const knowledgeStats = dashboardData?.knowledgeStats
  const { t } = useLanguage()

  // Create Supabase client
  const supabase = createClientComponentClient()

  // Get knowledge stats from dashboard cache
  const totalFaqs = knowledgeStats?.faqCount || 0
  const photoCount = knowledgeStats?.photoCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const photoLimit = knowledgeStats?.photoLimit || 0
  const isLoadingCount = isDashboardLoading

  // Photo search state - for intro only
  const [introSearchQuery, setIntroSearchQuery] = useState('')
  const [introSearchResults, setIntroSearchResults] = useState<any[]>([])
  const [showIntroResults, setShowIntroResults] = useState(false)
  const [isSearchingIntro, setIsSearchingIntro] = useState(false)
  const [selectedIntroPhoto, setSelectedIntroPhoto] = useState<any>(null)

  // Loading animation states for photo selection
  const [isIntroPhotoLoading, setIsIntroPhotoLoading] = useState(false)
  const introSearchResultsRef = useRef<HTMLDivElement>(null)
  const introSearchInputRef = useRef<HTMLInputElement>(null)

  // Add state for all photos
  const [allPhotos, setAllPhotos] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])

  // Edit mode state
  const [isIntroEditing, setIsIntroEditing] = useState(false)

  // Text content state
  const [introText, _setIntroText] = useState('')

  // Custom setters to track changes
  const setIntroText = (value: string) => {
    _setIntroText(value);
  }

  // Handle intro photo search
  const handleIntroPhotoSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setIntroSearchQuery(query);
    searchIntroPhotos(query);
  };

  // Fetch all photos from Supabase
  const fetchAllPhotos = async () => {
    try {
      // Wait for dashboard data to load before fetching photos
      if (!clientInfo?.client_id) {
        return
      }

      const clientId = clientInfo.client_id

      const { data, error } = await supabase
        .from('photos')
        .select('id, photo_id, photo_url, photo_file_path')
        .eq('client_id', clientId)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching photos:', error);
        return;
      }

      setAllPhotos(data || []);
    } catch (error) {
      console.error('Error in fetchAllPhotos:', error);
    }
  };

  // Search photos for intro section
  const searchIntroPhotos = async (query: string) => {
    if (!query.trim()) {
      setIntroSearchResults([]);
      setShowIntroResults(false);
      return;
    }

    setIsSearchingIntro(true);
    try {
      // First try to search locally if we have photos cached
      if (allPhotos.length > 0) {
        const filteredPhotos = allPhotos.filter(photo =>
          photo.photo_id.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5);

        if (filteredPhotos.length > 0) {
          setIntroSearchResults(filteredPhotos);
          setShowIntroResults(true);
          setIsSearchingIntro(false);
          return;
        }
      }

      // If no local results or no local data, fetch from server
      const clientId = clientInfo?.client_id;

      if (!clientId) {
        console.error('Client ID not found while searching photos');
        return;
      }

      // Search photos that match the query by photo_id
      const { data, error } = await supabase
        .from('photos')
        .select('id, photo_id, photo_url, photo_file_path')
        .eq('client_id', clientId)
        .ilike('photo_id', `%${query}%`)
        .limit(5);

      if (error) {
        console.error('Error searching photos:', error);
        return;
      }

      setIntroSearchResults(data || []);
      setShowIntroResults(data && data.length > 0);
    } catch (error) {
      console.error('Error in searchIntroPhotos:', error);
    } finally {
      setIsSearchingIntro(false);
    }
  };

  // Handle edit button click
  const handleEdit = () => {
    setIsIntroEditing(true);
  };

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative pb-16">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl px-4 py-3 border border-white/20 hover:border-white/30 transition-all duration-300 overflow-hidden"
          >
            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <Link href="/dashboard" className="group">
                <img
                  src="/images/white_tran_logo.svg"
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 group-hover:scale-105"
                />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <Link
                href="/dashboard"
                className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-zinc-300 hover:text-white bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg transition-all duration-200 group"
              >
                <svg
                  className="w-4 h-4 transform -translate-x-0.5 group-hover:-translate-x-1 transition-transform"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                {t('back')}
              </Link>

              <h1 className="text-2xl md:text-3xl font-bold font-title text-jade-purple-dark">
                {t('intro_outro')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* Top Section */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-6">
            {/* Left Card: Infographics */}
            <div
              className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-3 sm:p-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
              style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              }}>
              <div className="relative z-10">
                {/* Modern Circle Stats Grid */}
                <div className="grid grid-cols-2 gap-2 sm:gap-4">
                  {/* Business Insight Stat - Circle Design */}
                  <div className="flex flex-col items-center justify-center text-center">
                    <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                      {/* Progress Circle - SVG implementation - Stroke only */}
                      <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                        <circle
                          cx="50"
                          cy="50"
                          r="40"
                          fill="transparent"
                          stroke="rgb(63, 63, 73)"
                          strokeWidth="6"
                        />
                        <circle
                          cx="50"
                          cy="50"
                          r="40"
                          fill="transparent"
                          stroke="rgba(129, 82, 228, 0.9)"
                          strokeWidth="6"
                          strokeDasharray="251.2"
                          strokeDashoffset={251.2 * (1 - (totalFaqs / (totalFaqsLimit || 1)))}
                          transform="rotate(-90 50 50)"
                        />
                      </svg>

                      {/* Icon in Center */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-black/30 border border-white/20 flex items-center justify-center">
                          <FaBrain className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                        </div>
                      </div>
                    </div>

                    {/* Label */}
                    <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">{t('brain')}</p>

                    {/* Count */}
                    <p className="text-white text-xs sm:text-base font-body">
                      {isLoadingCount ?
                        <span className="flex justify-center">
                          <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                        </span>
                        : <>{totalFaqs} <span className="text-zinc-400">/ {totalFaqsLimit || 0}</span></>
                      }
                    </p>
                  </div>

                  {/* Photo Gallery Stat - Circle Design */}
                  <div className="flex flex-col items-center justify-center text-center">
                    <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                      {/* Progress Circle - SVG implementation - Stroke only */}
                      <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                        <circle
                          cx="50"
                          cy="50"
                          r="40"
                          fill="transparent"
                          stroke="rgb(63, 63, 73)"
                          strokeWidth="6"
                        />
                        <circle
                          cx="50"
                          cy="50"
                          r="40"
                          fill="transparent"
                          stroke="rgba(129, 82, 228, 0.9)"
                          strokeWidth="6"
                          strokeDasharray="251.2"
                          strokeDashoffset={251.2 * (1 - (photoCount / (photoLimit || 1)))}
                          transform="rotate(-90 50 50)"
                        />
                      </svg>

                      {/* Icon in Center */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-black/30 border border-white/20 flex items-center justify-center">
                          <FaImage className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                        </div>
                      </div>
                    </div>

                    {/* Label */}
                    <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">{t('photos')}</p>

                    {/* Count */}
                    <p className="text-white text-xs sm:text-base font-body">
                      {isLoadingCount ?
                        <span className="flex justify-center">
                          <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                        </span>
                        : <>{photoCount} <span className="text-zinc-400">/ {photoLimit || 0}</span></>
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Card: Action Buttons */}
            <div
              className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-3 sm:p-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
              style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              }}>
              <div className="relative z-10">
                {/* Buttons Grid */}
                <div className="grid grid-cols-1 gap-2 sm:gap-3">
                  <Link
                    href="/dashboard/knowledge"
                    className="bg-jade-purple/5 hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  >
                    {t('business_insight')}
                  </Link>
                  <Link
                    href="/dashboard/knowledge/photo"
                    className="bg-jade-purple/5 hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  >
                    {t('photo_gallery')}
                  </Link>
                  <Link
                    href="/dashboard/knowledge/intro"
                    className="bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  >
                    {t('intro_outro')}
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Intro Section */}
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 mb-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
          >
            <div className="relative z-10">
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                  <FaComments className="text-jade-purple mr-3 h-5 w-5" />
                  <h2 className="text-xl font-bold font-title">{t('intro_message')}</h2>
                </div>
                <div className="flex space-x-2">
                  {isIntroEditing ? (
                    <>
                      <button
                        className="bg-black/30 text-white hover:bg-black/50 border border-white/20 hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base"
                      >
                        {t('cancel')}
                      </button>
                      <button
                        className="bg-jade-purple text-white hover:bg-jade-purple-dark border border-jade-purple hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base"
                      >
                        {t('save')}
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => handleEdit()}
                      className="bg-white/5 hover:bg-white/20 text-white border border-jade-purple hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base"
                    >
                      {t('edit')}
                    </button>
                  )}
                </div>
              </div>
              <p className="text-white/60 text-sm mb-4 md:mb-6 font-body">
                {t('intro_description')}
              </p>

              {/* Photo Search Bar */}
              <div className="mb-4 relative">
                <div className="relative">
                  <input
                    type="text"
                    value={introSearchQuery}
                    onChange={handleIntroPhotoSearch}
                    onFocus={() => introSearchResults.length > 0 && setShowIntroResults(true)}
                    placeholder={t('search_photo_placeholder')}
                    disabled={!isIntroEditing}
                    className={`w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-400 focus:outline-none ${isIntroEditing ? 'focus:border-white/40' : 'cursor-not-allowed opacity-70'}`}
                    style={{
                      fontSize: '16px' // Prevent auto-zoom on mobile
                    }}
                    ref={introSearchInputRef}
                    autoComplete="off" // Prevent browser autocomplete from interfering
                    spellCheck="false" // Disable spell checking
                  />
                  {isSearchingIntro && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}
                </div>

                {/* Search Results Dropdown */}
                {showIntroResults && (
                  <div
                    className="absolute z-50 w-full mt-2 bg-zinc-800/95 border border-white/10 rounded-xl max-h-40 overflow-y-auto backdrop-blur-lg"
                    ref={introSearchResultsRef}
                  >
                    {introSearchResults.length > 0 ? (
                      introSearchResults.map(photo => (
                        <div
                          key={photo.id}
                          className="flex items-center gap-3 p-3 hover:bg-white/5 cursor-pointer border-b border-white/5 last:border-0 transition-colors duration-200"
                          style={{
                            transition: 'all 0.2s ease'
                          }}
                        >
                          {/* Photo Thumbnail - Optimized */}
                          <PhotoThumbnail
                            photo={photo}
                            className="w-10 h-10"
                          />
                          {/* Photo ID */}
                          <div className="flex-1 truncate">
                            <p className="text-white truncate">{photo.photo_id}</p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-3 text-zinc-400 text-center">
                        No photos found
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Answer Input */}
              <div className="mb-4">
                <div
                  className={`px-2 md:px-4 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-500 focus:outline-none ${isIntroEditing ? 'hover:border-jade-purple cursor-pointer' : 'opacity-70'} flex items-center min-h-[42px] relative`}
                >
                  {introText ? (
                    <span className="truncate pr-10">{introText}</span>
                  ) : (
                    <span className="text-zinc-500 truncate">{t('enter_welcome_message')}</span>
                  )}

                  {/* Microphone button */}
                  <button
                    type="button"
                    disabled={!isIntroEditing}
                    className={`absolute right-2 p-1.5 rounded-full transition-colors ${isIntroEditing ? 'bg-black/40 text-white/70 hover:bg-jade-purple hover:border-jade-purple-dark hover:text-white border border-white/20' : 'bg-black/30 text-white/30 cursor-not-allowed border border-white/10'}`}
                    title={isIntroEditing ? "Record intro" : "Edit mode required"}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

        </motion.div>
      </div>
      <Footer />
    </div>
  );
}

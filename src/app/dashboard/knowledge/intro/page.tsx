'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { useDashboardData } from '@/hooks/useOptimizedData'
import { FaBrain, FaImage, FaComments } from 'react-icons/fa'
import { useLanguage } from '@/context/LanguageContext'

export default function IntroPage() {
  // Use dashboard cache for knowledge stats and client info
  const { data: dashboardData, loading: isDashboardLoading } = useDashboardData()
  const knowledgeStats = dashboardData?.knowledgeStats
  const { t } = useLanguage()

  // Get knowledge stats from dashboard cache
  const totalFaqs = knowledgeStats?.faqCount || 0
  const photoCount = knowledgeStats?.photoCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const photoLimit = knowledgeStats?.photoLimit || 0
  const isLoadingCount = isDashboardLoading

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative pb-16">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl px-4 py-3 border border-white/20 hover:border-white/30 transition-all duration-300 overflow-hidden"
          >
            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <Link href="/dashboard" className="group">
                <img
                  src="/images/white_tran_logo.svg"
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 group-hover:scale-105"
                />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <Link
                href="/dashboard"
                className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-zinc-300 hover:text-white bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg transition-all duration-200 group"
              >
                <svg
                  className="w-4 h-4 transform -translate-x-0.5 group-hover:-translate-x-1 transition-transform"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                {t('back')}
              </Link>

              <h1 className="text-2xl md:text-3xl font-bold font-title text-jade-purple-dark">
                {t('intro_outro')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* Top Section */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-6">
            {/* Left Card: Infographics */}
            <div
              className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-3 sm:p-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
              style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              }}>
              <div className="relative z-10">
                {/* Modern Circle Stats Grid */}
                <div className="grid grid-cols-2 gap-2 sm:gap-4">
                  {/* Business Insight Stat - Circle Design */}
                  <div className="flex flex-col items-center justify-center text-center">
                    <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                      {/* Progress Circle - SVG implementation - Stroke only */}
                      <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                        <circle
                          cx="50"
                          cy="50"
                          r="40"
                          fill="transparent"
                          stroke="rgb(63, 63, 73)"
                          strokeWidth="6"
                        />
                        <circle
                          cx="50"
                          cy="50"
                          r="40"
                          fill="transparent"
                          stroke="rgba(129, 82, 228, 0.9)"
                          strokeWidth="6"
                          strokeDasharray="251.2"
                          strokeDashoffset={251.2 * (1 - (totalFaqs / (totalFaqsLimit || 1)))}
                          transform="rotate(-90 50 50)"
                        />
                      </svg>

                      {/* Icon in Center */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-black/30 border border-white/20 flex items-center justify-center">
                          <FaBrain className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                        </div>
                      </div>
                    </div>

                    {/* Label */}
                    <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">{t('brain')}</p>

                    {/* Count */}
                    <p className="text-white text-xs sm:text-base font-body">
                      {isLoadingCount ?
                        <span className="flex justify-center">
                          <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                        </span>
                        : <>{totalFaqs} <span className="text-zinc-400">/ {totalFaqsLimit || 0}</span></>
                      }
                    </p>
                  </div>

                  {/* Photo Gallery Stat - Circle Design */}
                  <div className="flex flex-col items-center justify-center text-center">
                    <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                      {/* Progress Circle - SVG implementation - Stroke only */}
                      <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                        <circle
                          cx="50"
                          cy="50"
                          r="40"
                          fill="transparent"
                          stroke="rgb(63, 63, 73)"
                          strokeWidth="6"
                        />
                        <circle
                          cx="50"
                          cy="50"
                          r="40"
                          fill="transparent"
                          stroke="rgba(129, 82, 228, 0.9)"
                          strokeWidth="6"
                          strokeDasharray="251.2"
                          strokeDashoffset={251.2 * (1 - (photoCount / (photoLimit || 1)))}
                          transform="rotate(-90 50 50)"
                        />
                      </svg>

                      {/* Icon in Center */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-black/30 border border-white/20 flex items-center justify-center">
                          <FaImage className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                        </div>
                      </div>
                    </div>

                    {/* Label */}
                    <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">{t('photos')}</p>

                    {/* Count */}
                    <p className="text-white text-xs sm:text-base font-body">
                      {isLoadingCount ?
                        <span className="flex justify-center">
                          <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                        </span>
                        : <>{photoCount} <span className="text-zinc-400">/ {photoLimit || 0}</span></>
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Card: Action Buttons */}
            <div
              className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-3 sm:p-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
              style={{
                boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
              }}>
              <div className="relative z-10">
                {/* Buttons Grid */}
                <div className="grid grid-cols-1 gap-2 sm:gap-3">
                  <Link
                    href="/dashboard/knowledge"
                    className="bg-jade-purple/5 hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  >
                    {t('business_insight')}
                  </Link>
                  <Link
                    href="/dashboard/knowledge/photo"
                    className="bg-jade-purple/5 hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  >
                    {t('photo_gallery')}
                  </Link>
                  <Link
                    href="/dashboard/knowledge/intro"
                    className="bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"
                  >
                    {t('intro_outro')}
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Intro Section */}
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 mb-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
          >
            <div className="relative z-10">
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                  <FaComments className="text-jade-purple mr-3 h-5 w-5" />
                  <h2 className="text-xl font-bold font-title">{t('intro_message')}</h2>
                </div>
                <div className="flex space-x-2">
                  <button
                    className="bg-white/5 hover:bg-white/20 text-white border border-jade-purple hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base"
                  >
                    {t('edit')}
                  </button>
                </div>
              </div>
              <p className="text-white/60 text-sm mb-4 md:mb-6 font-body">
                {t('intro_description')}
              </p>

              {/* Photo Search Bar */}
              <div className="mb-4 relative">
                <div className="relative">
                  <input
                    type="text"
                    placeholder={t('search_photo_placeholder')}
                    disabled={true}
                    className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-400 focus:outline-none cursor-not-allowed opacity-70"
                    style={{
                      fontSize: '16px' // Prevent auto-zoom on mobile
                    }}
                    autoComplete="off" // Prevent browser autocomplete from interfering
                    spellCheck="false" // Disable spell checking
                  />
                </div>
              </div>

              {/* Answer Input */}
              <div className="mb-4">
                <div
                  className="px-2 md:px-4 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-500 focus:outline-none opacity-70 flex items-center min-h-[42px] relative"
                >
                  <span className="text-zinc-500 truncate">{t('enter_welcome_message')}</span>

                  {/* Microphone button */}
                  <button
                    type="button"
                    disabled={true}
                    className="absolute right-2 p-1.5 rounded-full transition-colors bg-black/30 text-white/30 cursor-not-allowed border border-white/10"
                    title="Edit mode required"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

        </motion.div>
      </div>
      <Footer />
    </div>
  );
}
